"use client";

import React, { useState, useEffect, useC<PERSON>back, useMemo } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { 
  FolderIcon, 
  DocumentIcon, 
  ArrowLeftIcon,
  CloudArrowUpIcon,
  TrashIcon,
  PencilIcon,
  ShareIcon,
  ArrowDownTrayIcon,
  EyeIcon,
  PhotoIcon,
  FilmIcon,
  DocumentTextIcon,
  ArchiveBoxIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon,
  FolderPlusIcon
} from '@heroicons/react/24/outline';
import { FolderService } from '@/api/services/folderService';
import { FileService } from '@/api/services/fileService';
import { ChunkedUploadService } from '@/api/services/chunkedUploadService';
import { ApiClient } from '@/api/core/apiClient';
import { useAuth } from '@/contexts/AuthContext';
import { FileUploadZone } from '@/components/FileUploadZone';
import { RoleGuard, AdminOnly } from '@/components/auth/RoleGuard';
import { DeleteConfirmationModal } from '@/components/modals/DeleteConfirmationModal';
import { CreateFolderModal } from '@/components/modals/CreateFolderModal';
import { FileDetailModal } from '@/components/modals/FileDetailModal';
import { ShareModal } from '@/components/modals/ShareModal';
import { BulkOperationsModal } from '@/components/modals/BulkOperationsModal';
import { FilePreviewModal } from '@/components/modals/FilePreviewModal';
import {
  FolderDto,
  FileDto,
  FolderContentsResponse,
  UploadOptions,
  ChunkedUploadOptions,
  FolderCreateData,
  SortField,
  SortDirection
} from '@/api/types/interfaces';

interface UploadProgress {
  fileId: string;
  fileName: string;
  progress: number;
  status: 'uploading' | 'completed' | 'error';
  error?: string;
}

interface ContentItem {
  id: string;
  name: string;
  type: 'folder' | 'file';
  createdAt: string;
  updatedAt: string;
  ownerId: string;
  ownerName?: string;
  // Folder specific properties
  path?: string;
  fileCount?: number;
  subfolderCount?: number;
  // File specific properties
  displayName?: string;
  fileSize?: number;
  mimeType?: string;
  description?: string;
}

interface FolderDetailState {
  folder: FolderDto | null;
  contents: ContentItem[];
  loading: boolean;
  uploading: boolean;
  error: string | null;
  uploadProgress: UploadProgress[];
  selectedFiles: Set<string>;
  showUploadZone: boolean;
  showDeleteModal: boolean;
  deleting: boolean;
  showNewFolderModal: boolean;
  // New modal states
  showFileDetailModal: boolean;
  showShareModal: boolean;
  showBulkOperationsModal: boolean;
  showFilePreviewModal: boolean;
  selectedFile: FileDto | null;
  shareItem: { id: string; type: 'file' | 'folder'; name: string } | null;
}

export default function FolderDetailPage() {
  const { user } = useAuth();
  const router = useRouter();
  const params = useParams();
  const folderId = params.folderId as string;

  // API services
  const apiClient = useMemo(() => new ApiClient(
    process.env.NEXT_PUBLIC_API_BASE_URL || 'https://localhost:7040',
    user?.access_token || ''
  ), [user?.access_token]);

  const folderService = useMemo(() => new FolderService(apiClient), [apiClient]);
  const fileService = useMemo(() => new FileService(apiClient), [apiClient]);
  const chunkedUploadService = useMemo(() => new ChunkedUploadService(apiClient), [apiClient]);

  const [state, setState] = useState<FolderDetailState>({
    folder: null,
    contents: [],
    loading: true,
    uploading: false,
    error: null,
    uploadProgress: [],
    selectedFiles: new Set(),
    showUploadZone: false,
    showDeleteModal: false,
    deleting: false,
    showNewFolderModal: false,
    // Initialize new modal states
    showFileDetailModal: false,
    showShareModal: false,
    showBulkOperationsModal: false,
    showFilePreviewModal: false,
    selectedFile: null,
    shareItem: null
  });

  // Load folder details and contents
  const loadFolderDetails = useCallback(async () => {
    if (!user?.access_token || !folderId) return;

    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      // Load folder details
      const folderDetails = await folderService.getById(folderId);
      
      // Load folder contents (both folders and files)
      const contentsResponse: FolderContentsResponse = await folderService.getContents(folderId, {
        page: 1,
        pageSize: 100,
        sortBy: SortField.CreatedAt,
        sortDirection: SortDirection.DESC
      });

      // Transform folders and files into ContentItem format
      const folderItems: ContentItem[] = contentsResponse.folders.map(folder => ({
        id: folder.id,
        name: folder.name,
        type: 'folder' as const,
        createdAt: folder.createdAt,
        updatedAt: folder.updatedAt,
        ownerId: folder.ownerId,
        ownerName: folder.ownerName,
        path: folder.path,
        fileCount: folder.fileCount,
        subfolderCount: folder.subfolderCount
      }));

      const fileItems: ContentItem[] = contentsResponse.files.map(file => ({
        id: file.id,
        name: file.name,
        type: 'file' as const,
        createdAt: file.createdAt,
        updatedAt: file.updatedAt,
        ownerId: file.ownerId,
        ownerName: file.ownerName,
        displayName: file.displayName,
        fileSize: file.fileSize,
        mimeType: file.mimeType,
        description: file.description
      }));

      // Combine and sort by creation date (newest first)
      const allContents = [...folderItems, ...fileItems]
        .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

      setState(prev => ({
        ...prev,
        folder: folderDetails,
        contents: allContents,
        loading: false
      }));

    } catch (error: any) {
      console.error('Failed to load folder details:', error);
      setState(prev => ({
        ...prev,
        loading: false,
        error: 'Failed to load folder details. Please try again.'
      }));
    }
  }, [user?.access_token, folderId, folderService]);

  // Initial load
  useEffect(() => {
    loadFolderDetails();
  }, [loadFolderDetails]);

  // Handle file upload
  const handleFileUpload = useCallback(async (files: File[]) => {
    if (!user?.access_token || !folderId) return;

    setState(prev => ({ 
      ...prev, 
      uploading: true,
      uploadProgress: files.map(file => ({
        fileId: `${file.name}-${Date.now()}`,
        fileName: file.name,
        progress: 0,
        status: 'uploading' as const
      }))
    }));

    const uploadResults: UploadProgress[] = [];

    for (const file of files) {
      const fileId = `${file.name}-${Date.now()}`;
      
      try {
        const uploadOptions: UploadOptions = {
          parentFolderId: folderId,
          displayName: file.name,
          syncToGoogleDrive: false,
          overwriteExisting: false
        };

        // Use chunked upload for large files (>10MB)
        let uploadedFile: FileDto; // Store upload result
        
        if (file.size > 10 * 1024 * 1024) {
          const chunkedOptions: ChunkedUploadOptions = {
            ...uploadOptions,
            chunkSize: 1024 * 1024, // 1MB chunks
            onProgress: (progressData) => {
              setState(prev => ({
                ...prev,
                uploadProgress: prev.uploadProgress.map(p => 
                  p.fileId === fileId ? { ...p, progress: progressData.progress } : p
                )
              }));
            }
          };
          uploadedFile = await chunkedUploadService.uploadLargeFile(file, chunkedOptions);
        } else {
          uploadedFile = await fileService.uploadWithProgress(
            file,
            uploadOptions,
            (progressEvent: any) => {
              const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
              setState(prev => ({
                ...prev,
                uploadProgress: prev.uploadProgress.map(p => 
                  p.fileId === fileId ? { ...p, progress } : p
                )
              }));
            }
          );
        }

        console.log('File uploaded successfully:', uploadedFile);

        uploadResults.push({
          fileId,
          fileName: file.name,
          progress: 100,
          status: 'completed'
        });

      } catch (error: any) {
        console.error(`Failed to upload ${file.name}:`, error);
        uploadResults.push({
          fileId,
          fileName: file.name,
          progress: 0,
          status: 'error',
          error: error.message || 'Upload failed'
        });
      }
    }

    setState(prev => ({
      ...prev,
      uploading: false,
      uploadProgress: uploadResults
    }));

    // Reload folder contents after upload
    setTimeout(() => {
      loadFolderDetails();
      setState(prev => ({ ...prev, uploadProgress: [] }));
    }, 2000);

  }, [user?.access_token, folderId, fileService, chunkedUploadService, loadFolderDetails]);

  // Handle file download
  const handleFileDownload = useCallback(async (item: ContentItem) => {
    if (item.type !== 'file') return;
    
    try {
      // Get presigned download URL
      const response = await fileService.download(item.id, { presigned: true });
      
      if ('url' in response) {
        // Create a temporary download link
        const link = document.createElement('a');
        link.href = response.url;
        link.download = item.displayName || item.name;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } else {
        // Fallback: direct blob download
        const blob = response as Blob;
        const downloadUrl = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = item.displayName || item.name;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(downloadUrl);
      }
    } catch (error: any) {
      console.error('Failed to download file:', error);
      setState(prev => ({
        ...prev,
        error: 'Failed to download file. Please try again.'
      }));
    }
  }, [fileService]);

  // Handle file delete
  const handleFileDelete = useCallback(async (fileId: string) => {
    if (!confirm('Are you sure you want to delete this file?')) return;

    try {
      await fileService.delete(fileId);
      loadFolderDetails(); // Reload contents
    } catch (error: any) {
      console.error('Failed to delete file:', error);
      setState(prev => ({
        ...prev,
        error: 'Failed to delete file. Please try again.'
      }));
    }
  }, [fileService, loadFolderDetails]);

  // Handle folder delete confirmation
  const handleFolderDeleteClick = useCallback(() => {
    setState(prev => ({ ...prev, showDeleteModal: true }));
  }, []);

  // Handle folder delete
  const handleFolderDelete = useCallback(async () => {
    if (!state.folder) return;

    setState(prev => ({ ...prev, deleting: true }));

    try {
      await folderService.delete(state.folder.id);
      router.push('/manager-folders');
    } catch (error: any) {
      console.error('Failed to delete folder:', error);
      setState(prev => ({
        ...prev,
        deleting: false,
        showDeleteModal: false,
        error: 'Failed to delete folder. Please try again.'
      }));
    }
  }, [state.folder, folderService, router]);

  // Handle create subfolder
  const handleCreateSubfolder = useCallback(async (name: string, description?: string) => {
    if (!user?.access_token || !folderId) {
      console.error('Missing required data for creating subfolder:', { 
        hasToken: !!user?.access_token, 
        folderId 
      });
      setState(prev => ({
        ...prev,
        showNewFolderModal: false,
        error: 'Cannot create subfolder: missing authentication or parent folder ID.'
      }));
      return;
    }
    
    try {
      const createData: FolderCreateData = {
        name,
        description,
        parentFolderId: folderId // Use folderId from router params directly
      };
      
      console.log('Creating subfolder with data:', createData);
      
      const newFolder = await folderService.create(createData);
      
      console.log('Subfolder created successfully:', newFolder);
      
      setState(prev => ({ ...prev, showNewFolderModal: false }));
      
      // Reload folder contents to show the new subfolder
      loadFolderDetails();
    } catch (error: any) {
      console.error('Failed to create subfolder:', error);
      setState(prev => ({
        ...prev,
        showNewFolderModal: false,
        error: error?.message || 'Failed to create subfolder. Please try again.'
      }));
    }
  }, [user?.access_token, folderId, folderService, loadFolderDetails]);

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Format date
  const formatDate = (dateString: string) => {
    if (!dateString || dateString === '' || dateString === 'undefined' || dateString === 'null') {
      return 'N/A';
    }
    
    try {
      const date = new Date(dateString);
      // Check if date is valid
      if (isNaN(date.getTime())) {
        return 'N/A';
      }
      
      return new Intl.DateTimeFormat('vi-VN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      }).format(date);
    } catch (error) {
      console.warn('Invalid date string:', dateString);
      return 'N/A';
    }
  };

  // Get file icon
  const getFileIcon = (item: ContentItem) => {
    if (item.type !== 'file') return null;

    const extension = item.name.split('.').pop()?.toLowerCase();

    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'].includes(extension || '')) {
      return <PhotoIcon className="w-5 h-5 text-purple-500" />;
    }
    if (['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'].includes(extension || '')) {
      return <FilmIcon className="w-5 h-5 text-red-500" />;
    }
    if (['pdf'].includes(extension || '')) {
      return <DocumentTextIcon className="w-5 h-5 text-red-600" />;
    }
    return <DocumentIcon className="w-5 h-5 text-gray-500" />;
  };

  // New handler functions for enhanced features
  const handleFileClick = (file: ContentItem) => {
    if (file.type !== 'file') return;

    // Convert ContentItem to FileDto for the modal
    const fileDto: FileDto = {
      id: file.id,
      name: file.name,
      displayName: file.displayName || file.name,
      description: file.description || '',
      fileSize: file.fileSize || 0,
      mimeType: file.mimeType || '',
      createdAt: file.createdAt,
      updatedAt: file.updatedAt,
      ownerId: file.ownerId,
      ownerName: file.ownerName,
      tags: [],
      parentFolderId: folderId,
      isArchived: false,
      version: 1,
      filePath: '',
      storageProvider: 'local' as any,
      permissions: [],
      isShared: false
    };

    setState(prev => ({
      ...prev,
      selectedFile: fileDto,
      showFileDetailModal: true
    }));
  };

  const handleFilePreview = (file: ContentItem) => {
    if (file.type !== 'file') return;

    // Convert ContentItem to FileDto for the modal
    const fileDto: FileDto = {
      id: file.id,
      name: file.name,
      displayName: file.displayName || file.name,
      description: file.description || '',
      fileSize: file.fileSize || 0,
      mimeType: file.mimeType || '',
      createdAt: file.createdAt,
      updatedAt: file.updatedAt,
      ownerId: file.ownerId,
      ownerName: file.ownerName,
      tags: [],
      parentFolderId: folderId,
      isArchived: false,
      version: 1,
      filePath: '',
      storageProvider: 'local' as any,
      permissions: [],
      isShared: false
    };

    setState(prev => ({
      ...prev,
      selectedFile: fileDto,
      showFilePreviewModal: true
    }));
  };

  const handleShare = (item: ContentItem) => {
    setState(prev => ({
      ...prev,
      shareItem: {
        id: item.id,
        type: item.type,
        name: item.name
      },
      showShareModal: true
    }));
  };

  const handleBulkOperations = () => {
    setState(prev => ({
      ...prev,
      showBulkOperationsModal: true
    }));
  };

  const handleFileUpdated = (updatedFile: FileDto) => {
    console.log('File updated:', updatedFile);
    // Reload folder contents to reflect changes
    loadFolderDetails();
  };

  const handleFileDeleted = (fileId: string) => {
    console.log('File deleted:', fileId);
    // Reload folder contents to reflect changes
    loadFolderDetails();
  };

  const handleOperationComplete = () => {
    // Reload content and clear selections
    setState(prev => ({
      ...prev,
      selectedFiles: new Set(),
      showBulkOperationsModal: false
    }));

    loadFolderDetails();
  };

  if (state.loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-body text-gray-600">Loading folder details...</p>
        </div>
      </div>
    );
  }

  if (!state.folder) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <ExclamationTriangleIcon className="w-16 h-16 text-orange-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Folder Not Found</h2>
          <p className="text-gray-600 mb-4">The folder you're looking for doesn't exist or you don't have permission to access it.</p>
          <button
            onClick={() => router.push('/manager-folders')}
            className="btn-primary"
          >
            Back to Folders
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <button
              onClick={() => router.push('/manager-folders')}
              className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <ArrowLeftIcon className="w-5 h-5" />
            </button>
            <div className="flex items-center justify-between w-full">
              <div className="flex items-center gap-3">
                <FolderIcon className="w-8 h-8 text-blue-500" />
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">{state.folder.name}</h1>
                  <p className="text-sm text-gray-600">{state.folder.path}</p>
                </div>
              </div>
              
              {/* Folder Actions */}
              <div className="flex items-center gap-2">
                <RoleGuard requiredRoles={["User", "Manager", "Admin"]} showError={false}>
                  <button
                    onClick={() => setState(prev => ({ ...prev, showNewFolderModal: true }))}
                    className="btn-primary flex items-center gap-2"
                    title="Create Subfolder"
                  >
                    <FolderPlusIcon className="w-4 h-4" />
                    Create Subfolder
                  </button>
                </RoleGuard>
                
                <RoleGuard requiredRoles={["Manager", "Admin"]} showError={false}>
                  <button
                    className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                    title="Edit Folder"
                  >
                    <PencilIcon className="w-5 h-5" />
                  </button>
                  <button
                    className="p-2 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                    title="Share Folder"
                  >
                    <ShareIcon className="w-5 h-5" />
                  </button>
                </RoleGuard>
                
                <AdminOnly showError={false}>
                  <button
                    onClick={handleFolderDeleteClick}
                    className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                    title="Delete Folder"
                  >
                    <TrashIcon className="w-5 h-5" />
                  </button>
                </AdminOnly>
              </div>
            </div>
          </div>

          {state.folder.description && (
            <div className="bg-blue-50 border-l-4 border-blue-400 p-4 mb-4">
              <p className="text-blue-800">{state.folder.description}</p>
            </div>
          )}

          {/* Folder Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="bg-white rounded-lg p-4 border">
              <div className="text-2xl font-bold text-blue-600">
                {state.contents.filter(item => item.type === 'file').length}
              </div>
              <div className="text-sm text-gray-600">Files</div>
            </div>
            <div className="bg-white rounded-lg p-4 border">
              <div className="text-2xl font-bold text-green-600">
                {state.contents.filter(item => item.type === 'folder').length}
              </div>
              <div className="text-sm text-gray-600">Subfolders</div>
            </div>
            <div className="bg-white rounded-lg p-4 border">
              <div className="text-sm text-gray-600">Created</div>
              <div className="font-medium">{formatDate(state.folder.createdAt)}</div>
            </div>
            <div className="bg-white rounded-lg p-4 border">
              <div className="text-sm text-gray-600">Last Modified</div>
              <div className="font-medium">{formatDate(state.folder.updatedAt)}</div>
            </div>
          </div>
        </div>

        {/* Error Alert */}
        {state.error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex">
              <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <p className="text-sm text-red-800">{state.error}</p>
              </div>
              <button
                onClick={() => setState(prev => ({ ...prev, error: null }))}
                className="ml-auto text-red-400 hover:text-red-600"
              >
                <XCircleIcon className="h-5 w-5" />
              </button>
            </div>
          </div>
        )}

        {/* Upload Section */}
        <RoleGuard requiredRoles={["User", "Manager", "Admin"]} showError={false}>
          <div className="bg-white rounded-lg border p-6 mb-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900">Upload Files</h2>
              <button
                onClick={() => setState(prev => ({ ...prev, showUploadZone: !prev.showUploadZone }))}
                className="btn-primary flex items-center gap-2"
                disabled={state.uploading}
              >
                <CloudArrowUpIcon className="w-4 h-4" />
                {state.showUploadZone ? 'Hide Upload' : 'Show Upload'}
              </button>
            </div>

            {state.showUploadZone && (
              <div className="mb-4">
                <FileUploadZone
                  onFilesSelected={handleFileUpload}
                  multiple={true}
                  disabled={state.uploading}
                  accept="*/*"
                  maxSize={100 * 1024 * 1024} // 100MB
                />
              </div>
            )}

            {/* Upload Progress */}
            {state.uploadProgress.length > 0 && (
              <div className="space-y-2">
                <h3 className="font-medium text-gray-900">Upload Progress:</h3>
                {state.uploadProgress.map((upload) => (
                  <div key={upload.fileId} className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-sm font-medium text-gray-900">{upload.fileName}</span>
                        <span className="text-sm text-gray-600">{upload.progress}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full transition-all ${
                            upload.status === 'completed' ? 'bg-green-500' :
                            upload.status === 'error' ? 'bg-red-500' : 'bg-blue-500'
                          }`}
                          style={{ width: `${upload.progress}%` }}
                        />
                      </div>
                      {upload.error && (
                        <p className="text-xs text-red-600 mt-1">{upload.error}</p>
                      )}
                    </div>
                    {upload.status === 'completed' && (
                      <CheckCircleIcon className="w-5 h-5 text-green-500" />
                    )}
                    {upload.status === 'error' && (
                      <XCircleIcon className="w-5 h-5 text-red-500" />
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        </RoleGuard>

        {/* Combined Folders and Files Table */}
        <div className="bg-white rounded-lg border">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-900">
                Contents ({state.contents.length} items)
              </h2>
              {state.selectedFiles.size > 0 && (
                <div className="flex items-center gap-3">
                  <span className="text-sm text-gray-600">
                    {state.selectedFiles.size} selected
                  </span>
                  <button
                    onClick={handleBulkOperations}
                    className="px-3 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                  >
                    Bulk Actions
                  </button>
                </div>
              )}
            </div>
          </div>

          {state.contents.length === 0 ? (
            <div className="p-12 text-center">
              <DocumentIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No content found</h3>
              <p className="text-gray-500 mb-4">Upload files or create subfolders to get started.</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      <input
                        type="checkbox"
                        className="rounded border-gray-300"
                        onChange={(e) => {
                          if (e.target.checked) {
                            setState(prev => ({
                              ...prev,
                              selectedFiles: new Set(prev.contents.map(item => item.id))
                            }));
                          } else {
                            setState(prev => ({
                              ...prev,
                              selectedFiles: new Set()
                            }));
                          }
                        }}
                        checked={state.selectedFiles.size > 0 && state.selectedFiles.size === state.contents.length}
                      />
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Type
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Name
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Size
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Created Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Owner
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {state.contents.map((item) => (
                    <tr key={`${item.type}-${item.id}`} className="hover:bg-gray-50 transition-colors">
                      {/* Checkbox Column */}
                      <td className="px-6 py-4 whitespace-nowrap">
                        <input
                          type="checkbox"
                          className="rounded border-gray-300"
                          checked={state.selectedFiles.has(item.id)}
                          onChange={(e) => {
                            setState(prev => {
                              const newSelected = new Set(prev.selectedFiles);
                              if (e.target.checked) {
                                newSelected.add(item.id);
                              } else {
                                newSelected.delete(item.id);
                              }
                              return { ...prev, selectedFiles: newSelected };
                            });
                          }}
                        />
                      </td>

                      {/* Type Column */}
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          {item.type === 'folder' ? (
                            <div className="flex items-center gap-2">
                              <FolderIcon className="w-5 h-5 text-blue-500" />
                              <span className="text-sm font-medium text-blue-700">Folder</span>
                            </div>
                          ) : (
                            <div className="flex items-center gap-2">
                              {getFileIcon(item)}
                              <span className="text-sm font-medium text-gray-700">File</span>
                            </div>
                          )}
                        </div>
                      </td>

                      {/* Name Column */}
                      <td className="px-6 py-4">
                        <div className="max-w-0 flex-1">
                          {item.type === 'folder' ? (
                            <button
                              onClick={() => router.push(`/manager-folders/${item.id}`)}
                              className="text-sm font-medium text-blue-600 hover:text-blue-800 transition-colors truncate block"
                              title={`View folder: ${item.name}`}
                            >
                              {item.name}
                            </button>
                          ) : (
                            <div>
                              <button
                                onClick={() => handleFileClick(item)}
                                className="text-sm font-medium text-blue-600 hover:text-blue-800 transition-colors truncate block text-left"
                              >
                                {item.displayName || item.name}
                              </button>
                              {item.description && (
                                <div className="text-sm text-gray-500 truncate">
                                  {item.description}
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      </td>

                      {/* Size Column */}
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {item.type === 'folder' ? (
                          <span>{item.fileCount || 0} items</span>
                        ) : (
                          <span>{formatFileSize(item.fileSize || 0)}</span>
                        )}
                      </td>

                      {/* Created Date Column */}
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatDate(item.createdAt)}
                      </td>

                      {/* Owner Column */}
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {item.ownerName || 'Unknown'}
                      </td>

                      {/* Actions Column */}
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center justify-end gap-2">
                          {item.type === 'file' ? (
                            <>
                              <button
                                onClick={() => handleFilePreview(item)}
                                className="p-2 text-gray-400 hover:text-purple-600 hover:bg-purple-50 rounded-lg transition-colors"
                                title="Preview"
                              >
                                <EyeIcon className="w-4 h-4" />
                              </button>

                              <button
                                onClick={() => handleFileDownload(item)}
                                className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                                title="Download"
                              >
                                <ArrowDownTrayIcon className="w-4 h-4" />
                              </button>

                              <RoleGuard requiredRoles={["Manager", "Admin"]} showError={false}>
                                <button
                                  onClick={() => handleShare(item)}
                                  className="p-2 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                                  title="Share"
                                >
                                  <ShareIcon className="w-4 h-4" />
                                </button>
                              </RoleGuard>

                              <AdminOnly showError={false}>
                                <button
                                  onClick={() => handleFileDelete(item.id)}
                                  className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                                  title="Delete"
                                >
                                  <TrashIcon className="w-4 h-4" />
                                </button>
                              </AdminOnly>
                            </>
                          ) : (
                            <>
                              <button
                                onClick={() => router.push(`/manager-folders/${item.id}`)}
                                className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                                title="Open Folder"
                              >
                                <EyeIcon className="w-4 h-4" />
                              </button>
                              
                              <RoleGuard requiredRoles={["Manager", "Admin"]} showError={false}>
                                <button
                                  onClick={() => handleShare(item)}
                                  className="p-2 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                                  title="Share Folder"
                                >
                                  <ShareIcon className="w-4 h-4" />
                                </button>
                              </RoleGuard>

                              <AdminOnly showError={false}>
                                <button
                                  onClick={() => {
                                    if (confirm(`Are you sure you want to delete folder "${item.name}"?`)) {
                                      // Handle folder delete here
                                      console.log('Delete folder:', item.id);
                                    }
                                  }}
                                  className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                                  title="Delete Folder"
                                >
                                  <TrashIcon className="w-4 h-4" />
                                </button>
                              </AdminOnly>
                            </>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        isOpen={state.showDeleteModal}
        onClose={() => setState(prev => ({ ...prev, showDeleteModal: false }))}
        onConfirm={handleFolderDelete}
        title="Delete Folder"
        message="This action will permanently delete this folder and all its contents, including files and subfolders."
        itemName={state.folder?.name}
        itemType="Folder"
        danger={true}
        loading={state.deleting}
      />

      {/* Create Subfolder Modal */}
      <CreateFolderModal
        isOpen={state.showNewFolderModal}
        onClose={() => setState(prev => ({ ...prev, showNewFolderModal: false }))}
        onSubmit={handleCreateSubfolder}
        currentFolder={state.folder}
        loading={state.loading}
      />

      {/* File Detail Modal */}
      <FileDetailModal
        isOpen={state.showFileDetailModal}
        onClose={() => setState(prev => ({ ...prev, showFileDetailModal: false, selectedFile: null }))}
        file={state.selectedFile}
        fileService={fileService}
        onFileUpdated={handleFileUpdated}
        onFileDeleted={handleFileDeleted}
      />

      {/* Share Modal */}
      <ShareModal
        isOpen={state.showShareModal}
        onClose={() => setState(prev => ({ ...prev, showShareModal: false, shareItem: null }))}
        itemId={state.shareItem?.id || ''}
        itemType={state.shareItem?.type || 'file'}
        itemName={state.shareItem?.name || ''}
        fileService={state.shareItem?.type === 'file' ? fileService : undefined}
        folderService={state.shareItem?.type === 'folder' ? folderService : undefined}
      />

      {/* Bulk Operations Modal */}
      <BulkOperationsModal
        isOpen={state.showBulkOperationsModal}
        onClose={() => setState(prev => ({ ...prev, showBulkOperationsModal: false }))}
        selectedItems={{
          files: state.contents
            .filter(item => item.type === 'file' && state.selectedFiles.has(item.id))
            .map(item => item.id),
          folders: state.contents
            .filter(item => item.type === 'folder' && state.selectedFiles.has(item.id))
            .map(item => item.id)
        }}
        fileService={fileService}
        folderService={folderService}
        onOperationComplete={handleOperationComplete}
      />

      {/* File Preview Modal */}
      <FilePreviewModal
        isOpen={state.showFilePreviewModal}
        onClose={() => setState(prev => ({ ...prev, showFilePreviewModal: false, selectedFile: null }))}
        file={state.selectedFile}
        fileService={fileService}
      />
    </div>
  );
}